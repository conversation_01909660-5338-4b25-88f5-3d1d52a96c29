<?php

namespace App\Modules\Job\Constants;

use App\Exceptions\FailedRequestException;

class JobConnection
{
    public const ALL_JOB = [
        // Admin Jobs
        'admin_mail_jobs' => [
            'connection' => ConnectionSource::ADMIN,
            'table' => 'email_jobs',
            'name' => 'Admin: Mail',
        ],
        'admin_guest_request_jobs' => [
            'connection' => ConnectionSource::ADMIN,
            'table' => 'guest_request_jobs',
            'name' => 'Admin: Guest Request',
        ],
        'admin_domain_deletion_jobs' => [
            'connection' => ConnectionSource::ADMIN,
            'table' => 'domain_deletion_jobs',
            'name' => 'Admin: Domain Deletion',
        ],
        'admin_domain_cancellation_jobs' => [
            'connection' => ConnectionSource::ADMIN,
            'table' => 'domain_cancellation_jobs',
            'name' => 'Admin: Domain Cancellation',
        ],
        'admin_domain_restore_report_jobs' => [
            'connection' => ConnectionSource::ADMIN,
            'table' => 'domain_restore_report_jobs',
            'name' => 'Admin: Domain Restore Report',
        ],
        'admin_domain_client_hold_jobs' => [
            'connection' => ConnectionSource::ADMIN,
            'table' => 'domain_client_hold_jobs',
            'name' => 'Admin: Domain Client Hold',
        ],

        // Client Jobs
        'client_domain_exp_notif_sched_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'domain_exp_notif_sched_jobs',
            'name' => 'Expiry Notification',
        ],
        'client_mail_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'mail_jobs',
            'name' => 'Mail',
        ],
        'client_domain_registration_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'domain_registration_jobs',
            'name' => 'Domain Registration',
        ],
        'client_domain_renewal_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'domain_renewal_jobs',
            'name' => 'Domain Renewal',
        ],
        'client_domain_update_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'domain_update_jobs',
            'name' => 'Domain Update',
        ],
        'client_payment_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'payment_jobs',
            'name' => 'Payment',
        ],
        'client_domain_contacts_update_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'domain_contacts_update_jobs',
            'name' => 'Domain Contact Update',
        ],
        'client_contact_registration_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'contact_registration_jobs',
            'name' => 'Contact Registration',
        ],
        'client_contact_update_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'contact_update_jobs',
            'name' => 'Contact Update',
        ],
        'client_update_on_login_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'update_on_login_jobs',
            'name' => 'Update on Login',
        ],
        'client_domain_transfer_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'domain_transfer_jobs',
            'name' => 'Transfer Request',
        ],
        'client_cancel_domain_transfer_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'cancel_domain_transfer_jobs',
            'name' => 'Transfer Request Cancel',
        ],
        'client_send_transfer_request_response_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'send_transfer_request_response_jobs',
            'name' => 'Transfer Request Response',
        ],
        'client_poll_update_domain_transfer_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'poll_update_domain_transfer_jobs',
            'name' => 'Transfer Poll Notification',
        ],
        'client_domain_authcode_request_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'domain_authcode_request_jobs',
            'name' => 'Auth Code Request',
        ],
        'client_domain_authcode_update_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'domain_authcode_update_jobs',
            'name' => 'Auth Code Update',
        ],
        'client_domain_refresh_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'domain_refresh_jobs',
            'name' => 'Domain Refresh',
        ],
        'client_otp_mail_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'otp_mail_jobs',
            'name' => 'OTP Mail',
        ],
        'client_user_domain_export_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'user_domain_export_jobs',
            'name' => 'User Domain Export',
        ],
        'client_authentication_attempts_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'authentication_attempts_jobs',
            'name' => 'Authentication Attempts',
        ],
        'client_stripe_identity_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'stripe_identity_jobs',
            'name' => 'Stripe Identity',
        ],
        'client_market_place_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'market_place_jobs',
            'name' => 'Market Place',
        ],
        'client_domain_classification_api_jobs' => [
            'connection' => ConnectionSource::CLIENT,
            'table' => 'domain_classification_api_jobs',
            'name' => 'Domain Classification API',
        ]
    ];

    public static function getJobs(string $connection): array
    {
        return array_filter(self::ALL_JOB, function ($job) use ($connection) {
            return $job['connection'] === $connection;
        });
    }

    public static function get($key)
    {

        if (array_key_exists($key, self::ALL_JOB)) {
            return self::ALL_JOB[$key];
        }

        throw new FailedRequestException(400, 'job key does not exists', 'Invalid Request');
    }

    public static function getByTable($table)
    {
        foreach (self::ALL_JOB as $job) {
            if ($job['table'] === $table) {
                return $job;
            }
        }

        throw new FailedRequestException(400, 'Job table does not exist', 'Invalid Request');
    }

    // public static function keys(string $server): array
    // {
    //     $constant = strtoupper($server) . '_JOB';
    //     return constant("self::{$constant}");
    // }

    public static function findByTable(string $tableName)
    {

        foreach (self::ALL_JOB as $key => $element) {
            if (strcmp($element['table'], $tableName) == 0) {
                return $element;
            }
        }

        return null; // Return null if not found
    }
}
