<?php

namespace App\Modules\Client\Services;

use Illuminate\Support\Facades\DB;
use App\Traits\GroupDateFormatting;
use App\Traits\CursorPaginate;
use Illuminate\Http\Request;
use Carbon\Carbon;
use App\Modules\Client\Requests\SystemLogRequest;

class SystemLogService
{
    use GroupDateFormatting, CursorPaginate;

    private static $pageLimit = 20;

    protected $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    private function getSystemLogs()
    {
        $query = DB::table('public.system_transaction_histories')
            ->select('id', 'type', 'message', 'created_at')
            ->orderBy('id', 'desc');

        $this->applyTypeFilter($query);
        $this->applyDateFilter($query);
        
        $limit = $this->request->input('limit', 20);

        $paginator = $query->paginate($limit)->appends($this->request->all())->withQueryString();
        $paginationData = self::cursor($paginator, []);

        return [
            'data' => $this->formatLogs($paginationData['items']),
            'itemCount' => count($paginationData['items']),
            ...collect($paginationData)->except('items')->toArray()
        ];
    }

    private function applyTypeFilter($query)
    {
        if ($this->request->has('type')) {
            $query->where('type', $this->request->input('type'));
        }
    }

    private function applyDateFilter($query)
    {
        if (!$this->request->has('date')) {
            return;
        }

        $date = $this->request->input('date');

        switch ($date) {
            case 'today':
                $query->whereDate('created_at', Carbon::today());
                break;
            case 'yesterday':
                $query->whereDate('created_at', Carbon::yesterday());
                break;
            case 'last 7 days':
                $query->whereBetween('created_at', [Carbon::now()->subDays(7), Carbon::now()->subDays(1)]);
                break;
            case 'last 30 days':
                $query->where('created_at', '<=', Carbon::now()->subDays(30));
                break;
        }
    }

    public function prepareSystemLogView(SystemLogRequest $request)
    {
        $validated = $request->validated();
        $logs = $this->getSystemLogs();

        return [
            'logs' => $logs['data'],
            'itemName' => 'log',
            ...$logs
        ];
    }

    private function formatLogs($logs)
    {
        return collect($logs)->map(function ($log) {
            $log->formatted_created_at = $this->formatDate($log->created_at);
            return $log;
        });
    }
}