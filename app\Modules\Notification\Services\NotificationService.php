<?php

namespace App\Modules\Notification\Services;

use App\Traits\CursorPaginate;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class NotificationService
{
    use CursorPaginate;

    private static $pageLimit = 20;

    public static function getAll()
    {
        //$query = DB::client()->table('admin_notifications')
        //    ->orderBy('created_at', 'desc')
        //    ->paginate(self::$pageLimit);

        //return CursorPaginate::cursor($query);

        $limit = request()->all();
        $limit =  (empty($limit['limit'])) ? self::$pageLimit : $limit['limit'];

        $query['query'] = DB::client()->table('admin_notifications')
            ->orderBy('created_at', 'desc')
            ->paginate($limit)
            ->withQueryString();
        return CursorPaginate::cursor($query['query']);
    }

    public static function get_dropdown()
    {
        return DB::client()->table('admin_notifications')
            ->orderBy('created_at', 'desc')
            ->take(15)
            ->get();
    }

    public static function setRead($id)
    {
        DB::client()->table('admin_notifications')
            ->where('id', $id)
            ->whereNull('read_at')
            ->update([
                'read_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);

        $redirect_url = DB::client()->table('admin_notifications')
            ->where('id', $id)
            ->value('redirect_url');

        return $redirect_url;
    }

    public function getUnreadCount()
    {
        return DB::client()->table('admin_notifications')
            ->whereNull('read_at')
            ->count();
    }

    public static function sendFailedDeleteRequestNotif(string $domainName): void
    {
        self::sendNotification([
            'title' => 'Delete Request Failed',
            'message' => 'The delete request for ' . $domainName . ' has failed. Please check the domain status and expiry date.',
            'redirect_url' => 'domain.pending-delete.view',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);
    }

    public static function sendFailedClientHoldNotif(string $domainName): void
    {
        self::sendNotification([
            'title' => 'Client Hold Failed',
            'message' => 'The client hold for ' . $domainName . ' has failed. Please check the domain status.',
            'redirect_url' => 'domain.history',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);
    }

    public static function sendFailedRestoreReportNotif(string $domainName): void
    {
        self::sendNotification([
            'title' => 'Domain Restore Report Failed',
            'message' => 'The restore report submission for ' . $domainName . ' has failed. Please check the domain status and try again.',
            'redirect_url' => 'domain-redemption.view',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);
    }

    public static function sendFailedRestoreAcceptedNotif(string $domainName): void
    {
        self::sendNotification([
            'title' => 'Domain Restore Approved Failed',
            'message' => 'The restore approved submission for ' . $domainName . ' has failed. Please check the domain status and try again.',
            'redirect_url' => 'domain-redemption.view',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);
    }

    // Static functions for different notifications

    private static function sendNotification($field)
    {
        return DB::client()->table('admin_notifications')->insert($field);
    }
}
