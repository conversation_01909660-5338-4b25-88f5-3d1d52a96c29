<?php

namespace App\Listeners;

use App\Events\DomainHistoryEvent;
use App\Models\DomainTransactionHistory;
use Illuminate\Support\Facades\DB;

class LogDomainHistory
{
    public function handle(DomainHistoryEvent $event)
    {
        // DomainTransactionHistory::create([
        //     'domain_id' => $event->data['domain_id'],
        //     'type'      => $event->data['type'],
        //     'user_id'   => $event->data['user_id'],
        //     'status'    => $event->data['status'],
        //     'message'   => $event->data['message'],
        //     'payload'   => json_encode($event->data['payload']),
        // ]);

        DB::client()->table('domain_transaction_histories')->insert([
            'domain_id' => $event->data['domain_id'],
            'type'      => $event->data['type'],
            'user_id'   => $event->data['user_id'],
            'status'    => $event->data['status'],
            'message'   => $event->data['message'],
            'payload'   => json_encode($event->data['payload']),
            'created_at'=> now(),
            'updated_at'=> now(),
            ]);
    }
}

