<?php

namespace App\Modules\DomainHistory\Services;

use Illuminate\Support\Facades\DB;
use App\Modules\DomainHistory\Constants\TransactionType;
use Carbon\Carbon;
use App\Traits\CursorPaginate;
use App\Traits\GroupDateFormatting;

class DomainLogService
{
    use CursorPaginate, GroupDateFormatting;
    
    private static $pageLimit = 20;

    public function getDomainLogs($id, $filters)
    {
        $query = $this->buildDomainLogsQuery($id);

        $this->applyFiltersToQuery($query, $filters);

        $totalItems = $query->count();

        $domainLogs = $query->orderByDesc('domain_transaction_histories.created_at')
                            ->orderByDesc('domain_transaction_histories.id')
                            ->paginate(self::$pageLimit)
                            ->appends($filters);

        $logMessages = collect($domainLogs->items())->map(fn($log) => $log->message ?? 'No log message available');

        return $this->formatDomainLogsResponse($domainLogs, $logMessages, $totalItems);
    }

    public function getTransactionTypes()
    {
        return TransactionType::getConstants();
    }

    public function formatDate($dateString)
    {
        $date = Carbon::parse($dateString);
        $today = Carbon::today();
        $yesterday = Carbon::yesterday();

        $formattedDate = $date->format('M j, Y');

        if ($date->isSameDay($today)) {
            return "Today ($formattedDate)";
        } elseif ($date->isSameDay($yesterday)) {
            return "Yesterday ($formattedDate)";
        } else {
            $diffDays = round(abs($today->diffInDays($date, false)));

            if ($diffDays < 7) {
                return "$formattedDate ($diffDays days ago)";
            } elseif ($diffDays < 30) {
                $weeks = round($diffDays / 7);
                return "$formattedDate ($weeks week" . ($weeks > 1 ? 's' : '') . " ago)";
            } elseif ($diffDays < 365) {
                $months = round($diffDays / 30);
                return "$formattedDate ($months month" . ($months > 1 ? 's' : '') . " ago)";
            } else {
                $years = round($diffDays / 365);
                return "$formattedDate ($years year" . ($years > 1 ? 's' : '') . " ago)";
            }
        }
    }

    public function prepareDomainLogData($id, $filters = [])
    {
        $logs = $this->getDomainLogs($id, $filters);

        $formattedLogs = $logs['domainLogs']->map(fn($log) => $this->formatLog($log));

        return [
            'domainLogs' => $logs['domainLogs'],
            'formattedLogs' => $formattedLogs,
            'logMessages' => $logs['logMessages'],
            'transactionTypes' => $this->getTransactionTypes(),
            // 'ipDetails' => request()->ip(),
            'total' => $logs['totalItems'],
            'nextPageUrl' => $logs['domainLogs']->nextPageUrl(),
            'previousPageUrl' => $logs['domainLogs']->previousPageUrl(),
        ];
    }

    public function prepareInertiaData($id, $filters = [])
    {
        $logsData = $this->prepareDomainLogData($id, $filters);

        return array_merge($logsData, [
            'id' => $id,
            'itemCount' => count($logsData['domainLogs']),
            'total' => $logsData['total'],
        ]);
    }
    // Private functions

    private function formatDomainLogsResponse($domainLogs, $logMessages, $totalItems)
    {
        return [
            'domainLogs' => $domainLogs,
            'logMessages' => $logMessages,
            'totalItems' => $totalItems,
            'onFirstPage' => $domainLogs->onFirstPage(),
            'onLastPage' => $domainLogs->onLastPage(),
            'total' => $domainLogs->total(),
        ];
    }

    private function buildDomainLogsQuery($id)
    {
        return DB::table('public.domain_transaction_histories')
            ->join('public.domains', 'domain_transaction_histories.domain_id', '=', 'domains.id')
            ->leftJoin('public.users', 'domain_transaction_histories.user_id', '=', 'users.id')
            ->select(
                'domains.id as id',
                'domains.name as domain',
                'domain_transaction_histories.type as type',
                'domain_transaction_histories.message as message',
                DB::raw("COALESCE(users.email, 'Server') as domain_email"),
                'domain_transaction_histories.created_at as created_at'
            )
            ->where('domains.id', $id);
    }

    private function applyFiltersToQuery($query, $filters)
    {
        if (!empty($filters['type'])) {
            $query->where('domain_transaction_histories.type', $filters['type']);
        }

        if (!empty($filters['email'])) {
            $query->where(function($query) use ($filters) {
                $query->where('users.email', 'LIKE', $filters['email'] . '%')
                      ->orWhereNull('users.email', strtolower($filters['email']) === 'server');
            });
        }

        if (!empty($filters['date'])) {
            $this->applyDateFilter($query, $filters['date']);
        }
    }

    private function applyDateFilter($query, $dateFilter)
    {
        $today = now()->startOfDay();

        switch ($dateFilter) {
            case 'today':
                $query->whereDate('domain_transaction_histories.created_at', '=', $today->toDateString());
                break;
            case 'yesterday':
                $query->whereDate('domain_transaction_histories.created_at', '=', $today->copy()->subDay()->toDateString());
                break;
            case 'last 7 days':
                $query->where('domain_transaction_histories.created_at', '<=', $today->copy()->subDays(7));
                break;
            case 'last 30 days':
                $query->where('domain_transaction_histories.created_at', '<=', $today->copy()->subDays(30));
                break;
            default:
                if (Carbon::hasFormat($dateFilter, 'Y-m-d')) {
                    $query->whereDate('domain_transaction_histories.created_at', '=', $dateFilter);
                }
                break;
        }
    }

    private function formatLog($log)
    {
        $log->formatted_update = $this->formatDate($log->created_at);
        return $log;
    }
}
