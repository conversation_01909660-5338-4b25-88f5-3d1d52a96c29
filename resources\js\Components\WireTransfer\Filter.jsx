import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import { useRef, useState } from "react";
import { router } from "@inertiajs/react";
import useOutsideClick from "@/Util/useOutsideClick";
import { offFilter } from "@/Components/Util/Filter/FilterMethod";

export default function Filter() {
    const currentParams = route().params;
    const containerRef = useRef();

    const filterConfig = {
        container: {
            active: false,
            reload: false,
        },
        field: {
            orderby: {
                active: false,
                value: currentParams.orderby ? [currentParams.orderby] : [],
                type: "option",
                items: [
                    "created_at:desc",
                    "created_at:asc",
                    "amount:desc",
                    "amount:asc"
                ],
                name: "Order By",
            },
            status: {
                active: false,
                value: currentParams.status ? [currentParams.status] : [],
                type: "option",
                items: ["verified", "unverified", "rejected"],
                name: "Status",
            },
        },
    };

    const [filter, setFilter] = useState(filterConfig);
    const { field } = filter;

    useOutsideClick(containerRef, () => {
        setFilter(prevFilter => {
            const updatedFilter = offFilter(prevFilter);
            return {
                ...updatedFilter,
                field: Object.keys(updatedFilter.field).reduce((acc, key) => ({
                    ...acc,
                    [key]: {
                        ...updatedFilter.field[key],
                        active: false
                    }
                }), {})
            };
        });
    });

    const handleDisplayToggle = (newObject) => {
        setFilter({ ...filter, ...newObject });
    };

    const handleFieldUpdateValue = (key, value) => {
        const updatedFilter = {
            ...filter,
            container: { ...filter.container, active: false, reload: true },
            field: {
                ...filter.field,
                [key]: {
                    ...filter.field[key],
                    value: [value],
                    active: false,
                },
            },
        };

        setFilter(updatedFilter);

        const payload = {
            ...currentParams,
            [key]: value,
        };

        router.get(route("billing.wire.transfer"), payload);
    };

    return (
        <div className="flex items-center relative" ref={containerRef}>
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div>
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />
                <OptionFilter
                    fieldProp={field.orderby}
                    fieldKey="orderby"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <OptionFilter
                    fieldProp={field.status}
                    fieldKey="status"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
            </div>
        </div>
    );
}