import Item from "@/Components/Epp/Poll/Item";
import Checkbox from "@/Components/Checkbox";
import SecondaryButton from "@/Components/SecondaryButton";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import EmptyResult from "@/Components/Util/EmptyResult";
import AdminLayout from "@/Layouts/AdminLayout";
import useOutsideClick from "@/Util/useOutsideClick";
import { router } from "@inertiajs/react";
import { useRef, useState } from "react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { MdOutlineSortByAlpha, MdOutlineSettings, MdOutlineFilterAlt } from "react-icons/md";
// import { useState } from "react";
import { evaluate } from "@/Util/AxiosResponseHandler";
import axios from "axios";
import LoaderSpinner from "@/Components/LoaderSpinner";
import PollCursorPaginate from "@/Components/Util/PollCursorPaginate";
import Filter from "@/Components/Epp/Poll/Filter";

export default function Poll({
    items,
    onFirstPage = true,
    onLastPage = false,
    nextPageCursor = null,
    previousPageCursor = null
}) {
    // const [progress, setProgress] = useState(false);
    const { registry } = route().params;
    const polls = (items.polls = undefined ? {} : items.polls);
    // const cursor = (items.cursor = undefined ? {} : items.cursor);
    const config = {
        verisign: {
            active: "verisign" == registry,
        },
        verisign2: {
            active: "verisign2" == registry,
        },
        pir: {
            active: "pir" == registry,
        },
        pir2: {
            active: "pir2" == registry,
        },
    };
    const [show, setShow] = useState(false);
    const ref = useRef();

    useOutsideClick(ref, () => {
        setShow(false);
    });

    // const [selectAll, setSelectAll] = useState(false);
    // const [selectedItems, setSelectedItems] = useState([]);

    // const handleSelectAllChange = (e) => {
    //     const checked = getEventValue(e);
    //     setSelectAll(checked);
    //     setSelectedpoll(checked ? polls.map((item) => item.id) : []);
    // };

    // const handleItemCheckboxChange = (itemId, checked) => {
    //     setSelectedItems((prevSelectedItems) => {
    //         return checked
    //             ? [...prevSelectedItems, itemId]
    //             : prevSelectedItems.filter((id) => id !== itemId);
    //     });

    //     let temp = [...selectedItems];

    //     if (temp.includes(itemId)) {
    //         temp = temp.filter((e) => e != itemId);
    //     } else {
    //         temp.push(itemId);
    //     }

    //     temp.length == items.length ? setSelectAll(true) : setSelectAll(false);
    // };

    // const handleAcknowledgeNewPoll = async (targetRegistry) => {
    //     if (progress) return;
    //     const payload = { registry: targetRegistry };
    //     setProgress(true);
    //     toast.info(
    //         "Checking New Poll From " + targetRegistry + " , Please Wait."
    //     );

    //     let response = await axios
    //         .get(route("epp.poll.pop"), payload)
    //         .then((response) => {
    //             return response;
    //         })
    //         .catch((error) => {
    //             return error.response;
    //         });
    //     response = evaluate(response);
    //     if (response.success) {
    //         toast.success("Done, Refreshing Page.");
    //         router.get(route("epp.poll"), payload);
    //     } else {
    //         toast.error("Pulling Failed, Something Went Wrong.");
    //     }
    // };

    const [hasSpinner, setSpinner] = useState(false);

    router.on("start", () => {
        setSpinner(true);
    });

    router.on("finish", () => {
        setSpinner(false);
    });

    // const [hasActive, setActivetab] = useState(null);

    // const removeactivetab = () => {

    //     const parent = document.getElementById("parentElement");
    //     const children = parent.querySelectorAll(".child.bg-gray-100");
    //     children.forEach(child => {
    //         child.classList.remove("bg-gray-100");
    //     });

    // };

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1000px] mt-2 flex flex-col px-5 rounded-lg">{hasSpinner ? <LoaderSpinner ml='ml-96' h='h-12' w='w-12' position='fixed' /> : ""}</div>
            <div className="mx-auto container max-w-[1000px] mt-20 flex flex-col justify-between">
                {/* {registry != undefined && (
                    <div className="flex poll-center space-x-4 justify-end">
                        <SecondaryButton
                            processing={true}
                            onClick={() => handleAcknowledgeNewPoll(registry)}
                        >
                            {registry} : Acknowledge new poll
                        </SecondaryButton>
                    </div>
                )} */}
                {/* <div>SIZE: {items.polls.length}</div> */}
                <div
                    id="sample"
                    className="flex items-center space-x-2 flex-wrap min-h-[2rem]"
                >
                    <label className="flex items-center">
                        <MdOutlineFilterAlt />
                        <span className="ml-2 text-sm text-gray-600">
                            Filter:
                        </span>
                    </label>

                    <Filter />
                </div>
                <div className="flex poll-center flex-wrap cursor-pointer border-b text-default">
                    <a
                        href={route("epp.poll", { registry: "verisign", cursor: "" })}
                        className={
                            "hover:bg-gray-100 px-5 py-1 rounded-sm " +
                            `${config.verisign.active &&
                            "bg-gray-100 text-link"
                            }`
                        }
                    >
                        <span className=" text-inherit">Verisign</span>
                    </a>
                    <a
                        href={route("epp.poll", { registry: "pir", cursor: "" })}
                        className={
                            "hover:bg-gray-100 px-5 py-1 rounded-sm " +
                            `${config.pir.active && "bg-gray-100 text-link"}`
                        }
                    >
                        <span className=" text-inherit">PIR</span>
                    </a>
                </div>
                {registry == null ? (<EmptyResult message="Select registry to view poll" />) : (
                    <>
                        {polls == null ? (
                            <EmptyResult message="No data available for registry" />
                        ) : (
                            <>
                                <div className="pt-5">
                                    <table className="min-w-full text-left border-spacing-y-2.5 border-separate">
                                        <thead className=" bg-gray-50 text-sm">
                                            <tr>
                                                <th>
                                                    <span>Type</span>
                                                </th>

                                                <th>
                                                    <span>Name</span>
                                                </th>

                                                <th>
                                                    <span>Message</span>
                                                </th>

                                                <th>
                                                    <span>Received At</span>
                                                </th>
                                                {/* <th>
                                            <span className="text-xl">
                                                <MdOutlineSettings />
                                            </span>
                                        </th> */}
                                            </tr>
                                        </thead>
                                        <tbody className="text-sm">
                                            {polls != undefined &&
                                                Object.values(polls).map((e) => {
                                                    return (
                                                        <Item key={"pi" + e.id} item={e} registry={registry} />
                                                    );
                                                })}
                                        </tbody>
                                    </table>
                                </div>
                                {/* <CursorPaginate
                                    onFirstPage={onFirstPage}
                                    onLastPage={onLastPage}
                                    nextPageUrl={nextPageUrl}
                                    previousPageUrl={previousPageUrl}
                                    itemCount={itemCount}
                                    total={total}
                                    itemName={itemName}
                                /> */}
                                <PollCursorPaginate
                                    onFirstPage={onFirstPage}
                                    onLastPage={onLastPage}
                                    nextPageCursor={nextPageCursor}
                                    previousPageCursor={previousPageCursor}
                                />
                            </>
                        )}
                    </>
                )}
            </div>
        </AdminLayout>
    );
}
