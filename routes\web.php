<?php

use App\Modules\Client\Controllers\ClientController;
use App\Modules\Dashboard\Controllers\DashboardController;
use App\Modules\VIP\Controllers\VipController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Illuminate\Http\Request;
use <PERSON><PERSON><PERSON><PERSON>n\CodeCoverage\Report\Html\Dashboard;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/bad-request', function () {
    return Inertia::render('Errors/BadRequest');
})->name('bad-request');

Route::get('/invalid-password-reset-token', function () {
    return Inertia::render('Errors/InvalidPasswordResetToken');
})->name('invalid-password-reset-token');

Route::get('/', function () {
    return Inertia::render('Welcome', [
        'canLogin' => Route::has('login'),
        'canRegister' => Route::has('register'),
        'laravelVersion' => Application::VERSION,
        'phpVersion' => PHP_VERSION,
    ]);
})->name('home');

Route::get('/dashboard', [DashboardController::class, 'index'])->middleware(['auth', 'auth.active', 'registry.balance'])->name('dashboard');

Route::get('/403', function () {
    return Inertia::render('Errors/Error403');
})->middleware(['auth'])->name('page403');

require __DIR__.'/admin.php';
require __DIR__.'/activity.php';
require __DIR__.'/auth.php';
require __DIR__.'/client.php';
require __DIR__.'/epp.php';
require __DIR__.'/ip.php';
require __DIR__.'/job.php';
require __DIR__.'/notification.php';
require __DIR__.'/profile.php';
require __DIR__.'/request.php';
require __DIR__.'/setting.php';
require __DIR__.'/history.php';
require __DIR__.'/market.php';
require __DIR__.'/user_management.php';
require __DIR__.'/pending_delete.php';
require __DIR__.'/domain_redemption.php';
require __DIR__.'/billing.php';

Route::get('/fake-route', function (Request $request) {
    if (!$request->hasValidSignature()) {
        abort(403, 'Invalid or expired link.');
    }

    return "Valid signed URL!";
})->name('fake.route'); // Ensure the route name matches the one used in `temporarySignedRoute()`

// require __DIR__.'/mailable.php';

Route::middleware(['auth', 'auth.active', 'auth.permission.check', 'registry.balance'])->group(function () {
    // for a while
    
    Route::get('/system-logs', [App\Modules\Client\Controllers\SystemLogController::class, 'index'])
        ->name('system.logs');
});
