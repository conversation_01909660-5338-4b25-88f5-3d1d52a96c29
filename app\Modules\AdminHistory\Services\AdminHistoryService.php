<?php

namespace App\Modules\AdminHistory\Services;

use App\Traits\CursorPaginate;
use App\Traits\GroupDateFormatting;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminHistoryService
{
    use CursorPaginate, GroupDateFormatting;

    private $request;

    public static function instance(): self
    {
        return new self;
    }

    public function __construct($request = null)
    {
        $this->request = $request;
    }

    private function getAdminLogs()
    {
        $query = DB::table('admin_transaction_histories as ath')
            ->leftJoin('admins as a', 'ath.admin_id', '=', 'a.id')
            ->select([
                'ath.id',
                'ath.type',
                'ath.message',
                'ath.created_at',
                'a.name as admin_name'
            ])
            ->orderBy('ath.id', 'desc');

        $this->applyTypeFilter($query);
        $this->applyDateFilter($query);

        $limit = $this->request->input('limit', 20);

        $paginator = $query->paginate($limit)->appends($this->request->all())->withQueryString();
        $paginationData = self::cursor($paginator, []);

        return [
            'data' => $this->formatLogs($paginationData['items']),
            'itemCount' => count($paginationData['items']),
            ...collect($paginationData)->except('items')->toArray()
        ];
    }

    private function applyTypeFilter($query)
    {
        if ($this->request->has('type')) {
            $query->where('ath.type', $this->request->input('type'));
        }
    }

    private function applyDateFilter($query)
    {
        if (!$this->request->has('date')) {
            return;
        }

        $date = $this->request->input('date');

        switch ($date) {
            case 'today':
                $query->whereDate('ath.created_at', Carbon::today());
                break;
            case 'yesterday':
                $query->whereDate('ath.created_at', Carbon::yesterday());
                break;
            case 'last 7 days':
                $query->whereBetween('ath.created_at', [Carbon::now()->subDays(7), Carbon::now()->subDays(1)]);
                break;
            case 'last 30 days':
                $query->where('ath.created_at', '<=', Carbon::now()->subDays(30));
                break;
        }
    }

    private function formatLogs($logs)
    {
        return collect($logs)->map(function ($log) {
            return [
                'id' => $log->id,
                'type' => $log->type,
                'message' => $log->message,
                'created_at' => $log->created_at,
                'formatted_created_at' => $this->formatDate($log->created_at),
                'admin_name' => $log->admin_name ?? 'System'
            ];
        });
    }

    public function prepareAdminLogView($request)
    {
        $this->request = $request;
        $logs = $this->getAdminLogs();

        return [
            'logs' => $logs['data'],
            'itemName' => 'log',
            ...$logs
        ];
    }
}
