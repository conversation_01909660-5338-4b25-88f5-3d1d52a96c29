import React, { useEffect, useState } from "react";
import Checkbox from "@/Components/Checkbox";
import GuestLayout from "@/Layouts/GuestLayout";
import InputError from "@/Components/InputError";
import InputLabel from "@/Components/InputLabel";
import PrimaryButton from "@/Components/PrimaryButton";
import TextInput from "@/Components/TextInput";
import { Head, Link, useForm } from "@inertiajs/react";
import { MdOutlineRemoveRedEye } from "react-icons/md";
import { getEventValue } from "@/Util/TargetInputEvent";
import { AiFillEyeInvisible, AiOutlineEye } from "react-icons/ai";

// Import Jotai atoms
import {
    notifications,
    hasMore,
    pageNumber,
    hideBadge
} from "@/State/NotificationsDropdownJotaiState"
import { useAtom } from 'jotai';

export default function Login(props) {

    // Jotai state atoms
    const [notificationss, setNotifications] = useAtom(notifications);
    const [pageNumbers, setPageNumber] = useAtom(pageNumber);
    const [hasMores, setHasMore] = useAtom(hasMore);
    const [hideBadgeCount, setHideBadgeCount] = useAtom(hideBadge);

    const { status, canResetPassword } = props;

    const { data, setData, post, processing, errors, reset,  clearErrors } = useForm({
        email: "",
        password: "",
        remember: false,
    });
    useEffect(() => {
        return () => {
            reset("password");
        };
    }, []);

    const onHandleChange = (event) => {
        setData(event.target.name, getEventValue(event));
    };

    const submit = (e) => {
        e.preventDefault();

        clearErrors();

        //reset Jotai State to Default
        setHideBadgeCount(false);
        setNotifications([]);
        setHasMore(true);
        setPageNumber(1);


        post(route("login"));
    };

    const [show, setShow] = useState(false);
    const handleClick = () => {
        setShow(!show);
    };

    return (
        <GuestLayout>
            <Head title="Log in" />

            {/* {status && (
                <div className="mb-4 font-medium text-sm text-green-600">
                    {status}
                </div>
            )} */}
            <div className="block text-center py-6">
                <label className="text-gray-700 text-2xl">Admin Login</label>
            </div>
            <form onSubmit={submit}>
                <div>
                    <InputLabel forInput="email" value="Email" />

                    <TextInput
                        type="text"
                        name="email"
                        value={data.email}
                        placeholder="email@sample"
                        className="mt-1 block w-full "
                        autoComplete="username"
                        isFocused={true}
                        handleChange={onHandleChange}
                    />

                    <InputError message={errors.email} className="mt-2" />
                </div>

                <div className="mt-4">
                    <InputLabel forInput="password" value="Password" />
                <div className="items-center">
                    <TextInput
                        type={show ? "text" : "password"}
                        name="password"
                        value={data.password}
                        placeholder="account password"
                        className="mt-1 block w-full"
                        autoComplete="current-password"
                        handleChange={onHandleChange}
                    />
                    <div className="absolute ml-12">
                            <i
                                onClick={handleClick}
                                className="text-xl cursor-pointer hover:text-[red] relative bottom-8 left-80 w-1 inline-table"
                            >
                                {show ? (
                                    <AiFillEyeInvisible />
                                ) : (
                                    <AiOutlineEye />
                                )}
                            </i>
                        </div>
                </div>
                    <InputError message={errors.password} className="mt-2" />
                </div>

                <div className="block mt-4">
                    <label className="flex items-center">
                        <Checkbox
                            name="remember"
                            checked={data.remember}
                            handleChange={onHandleChange}
                        />
                        <span className="ml-2 text-sm text-gray-600">
                            Remember me
                        </span>
                    </label>
                </div>

                <div className="flex flex-col items-center justify-end mt-4 space-y-4">
                    <PrimaryButton className="w-full " processing={processing}>
                        Log in
                    </PrimaryButton>
                    <div className="flex flex-col items-center space-y-1">
                        <Link
                            href={route("password.request")}
                            className="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                        >
                            Forgot your password?
                        </Link>
                        <Link
                            href={route("home")}
                            className="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                        >
                            Go back to Home
                        </Link>
                    </div>
                </div>
            </form>
        </GuestLayout>
    );
}
