import DropDownContainer from "@/Components/DropDownContainer";
import useOutsideClick from "@/Util/useOutsideClick";
import { useRef, useState } from "react";
import { MdMoreVert } from "react-icons/md";
import { StatusBadge } from "./StatusBadge";
import convertToTitleCase from "@/Util/convertToTitleCase";

export default function Item({
    item,
    onViewRequest,
    onApproveRequest,
    onRejectRequest,
}) {
    const [show, setShow] = useState(false);
    const ref = useRef();

    useOutsideClick(ref, () => {
        setShow(false);
    });

    return (
        <>
            <tr className="">
                <td>
                    <label className="flex items-center pl-2 space-x-2">
                        <span className="cursor-pointer">
                            {item.domainName}
                        </span>
                    </label>
                </td>
                <td>
                    <span>{item.email}</span>
                </td>
                <td>
                    <span>{item.requested_at}</span>
                </td>
                <td>
                    <span>{item.rstatus}</span>
                </td>
                <td>
                    {<StatusBadge status={convertToTitleCase(item.status)} />}
                </td>
                <td>
                    <span ref={ref} className="relative">
                        <button
                            className="flex items-center"
                            onClick={() => setShow(!show)}
                        >
                            <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                        </button>
                        <DropDownContainer show={show}>
                            <button
                                className="text-black px-5 py-1 hover:bg-gray-100 justify-start flex"
                                onClick={() => onViewRequest(item)}
                            >
                                View Request
                            </button>
                            <button
                                className={`px-5 py-1 justify-start flex ${
                                    item.support_agent_id != null
                                        ? "text-gray-200 px-2 py-1 cursor-not-allowed"
                                        : "text-black px-2 py-1 hover:bg-gray-100"
                                }`}
                                onClick={() => onApproveRequest(item)}
                                disabled={item.support_agent_id != null}
                            >
                                Approve
                            </button>
                            <button
                                className={`hover:bg-gray-100 px-5 py-1 justify-start flex ${
                                    item.support_agent_id != null
                                        ? "text-gray-200 px-2 py-1 cursor-not-allowed"
                                        : "text-black px-2 py-1 hover:bg-gray-100"
                                }`}
                                onClick={() => onRejectRequest(item)}
                                disabled={item.support_agent_id != null}
                            >
                                Reject
                            </button>
                        </DropDownContainer>
                    </span>
                </td>
            </tr>
        </>
    );
}
