<?php

namespace App\Modules\Epp\Services;

use Illuminate\Support\Facades\Config;

class LocalEppUrl implements EppUrlInterface
{
    protected $key;

    protected $secretKey;

    public function __construct(string $key, string $secretKey)
    {
        $this->key = $key;
        $this->secretKey = $secretKey;
    }

    public function registry(string $registry)
    {
        return $this->key . Config::get('epp.v3_' . $registry);
    }

    public function poll(string $registry)
    {
        return $this->key . Config::get('poll.v3_' . $registry);
    }

    public function domain(string $registry)
    {
        return $this->key . Config::get('domain.v3_multiple_' . $registry);
    }


    public function secretKey()
    {
        return $this->secretKey;
    }
}
