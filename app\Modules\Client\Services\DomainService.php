<?php

namespace App\Modules\Client\Services;

use App\Modules\Client\Constants\DomainStatus;
use App\Traits\CursorPaginate;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class DomainService
{
    use CursorPaginate;

    private static $pageLimit = 20;

    public static function getDomainsOwner($id)
    {
        $result = DB::client()->table('users')->where('id', $id)->select('first_name as name')->first();

        return $result->name;
    }

    public static function checkOwnerExists($id)
    {
        $result = DB::client()->table('users')->where('id', $id)->exists();

        return $result;
    }

    public static function getUserDomain($request, $id)
    {
        $builder = DB::client()
            ->table('domains')
            ->join('registered_domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->join('extensions', 'extensions.id', '=', 'registered_domains.extension_id')
            ->where([['users.id', '=', $id], ['registered_domains.status', '!=', DomainStatus::DELETED]])
            ->select(
                'domains.id as domain_id',
                'domains.name as domain_name',
                'domains.expiry',
                'domains.created_at',
                'registered_domains.status',
                'registered_domains.extension_id',
                'extensions.name as extension_name',
            );

        self::whenHasStatus($builder, $request);
        self::whenHasTld($builder, $request);
        self::whenHasExpireDate($builder, $request);
        self::whenHasOrderby($builder, $request);

        $builder = $builder->paginate(self::$pageLimit);

        self::appendDomainStatus($builder);

        return CursorPaginate::cursor($builder, self::paramToURI($request));
    }

    private static function whenHasStatus(&$builder, $request)
    {
        $builder->when($request->has('status'), function (Builder $query) use ($request) {
            $status = $request->status;
            $now = Carbon::now()->getTimestampMs();
            switch ($status) {
                case 'Active':
                    $query->where([
                        ['registered_domains.status', '=', DomainStatus::OWNED],
                        ['domains.expiry', '>', $now],
                    ]);
                    break;
                case 'Expired':
                    $query->where('domains.expiry', '<=', $now);
                    break;
                case 'Transferred':
                    $query->where([
                        ['registered_domains.status', DomainStatus::TRANSFERRED],
                        ['domains.expiry', '>', $now],
                    ]);
                    break;
            }
        });
    }

    private static function whenHasTld(&$builder, $request)
    {
        $builder->when($request->has('tld'), function (Builder $query) use ($request) {
            $tldValues = explode(',', $request->tld);
            $query->whereIn('extensions.name', $tldValues);
        });
    }

    private static function whenHasExpireDate(&$builder, $request)
    {
        $minExpireTimestamp = null;
        $maxExpireTimestamp = null;

        if ($request->has('minExpireDate') && !empty($request->minExpireDate)) {
            try {
                $minExpireTimestamp = Carbon::createFromFormat('Y-m-d', $request->minExpireDate)->startOfDay()->getTimestampMs();
            } catch (\Exception $e) {
                // Invalid date format, skip this filter
            }
        }

        if ($request->has('maxExpireDate') && !empty($request->maxExpireDate)) {
            try {
                $maxExpireTimestamp = Carbon::createFromFormat('Y-m-d', $request->maxExpireDate)->endOfDay()->getTimestampMs();
            } catch (\Exception $e) {
                // Invalid date format, skip this filter
            }
        }

        if ($minExpireTimestamp !== null && $maxExpireTimestamp !== null) {
            if ($minExpireTimestamp > $maxExpireTimestamp) {
                return;
            }
            $builder = $builder->whereBetween('domains.expiry', [$minExpireTimestamp, $maxExpireTimestamp]);
        } elseif ($minExpireTimestamp !== null) {
            $builder = $builder->where('domains.expiry', '>=', $minExpireTimestamp);
        } elseif ($maxExpireTimestamp !== null) {
            $builder = $builder->where('domains.expiry', '<=', $maxExpireTimestamp);
        }
    }

    private static function whenHasOrderby(&$builder, $request)
    {
        $builder->when($request->has('orderby'), function (Builder $query) use ($request) {
            $orderby = explode(':', $request->orderby);
            switch ($orderby[0]) {
                case 'domain':
                    $query->orderBy('domain_name', $orderby[1]);
                    break;
                case 'expiry':
                    $query->orderBy('domains.expiry', $orderby[1]);
                    break;
                case 'created':
                    $query->orderBy('domains.created_at', $orderby[1]);
                    break;
            }
            $query->orderBy('domain_id', 'desc');
        })
            ->when(! $request->has('orderby'), function (Builder $query) {
                $query->orderBy('domains.expiry', 'asc');
                $query->orderBy('domain_id', 'desc');
            });
    }

    private static function appendDomainStatus(&$builder)
    {
        $timeNow = Carbon::now()->getTimestampMs();

        foreach ($builder->items() as $item) {
            $itemDomainStatus = 'Unknown';

            if (strcmp(DomainStatus::OWNED, $item->status) === 0) {
                $itemDomainStatus = 'Active';
            }
            if (strcmp(DomainStatus::TRANSFERRED, $item->status) === 0) {
                $itemDomainStatus = 'Transferred';
            }

            $item->domainStatus = $item->expiry <= $timeNow ? 'Expired' : $itemDomainStatus;
        }
    }

    private static function paramToURI($request)
    {
        $param = [];

        if ($request->has('status')) {
            $param[] = 'status='.$request->status;
        }

        if ($request->has('tld')) {
            $param[] = 'tld='.$request->tld;
        }

        if ($request->has('orderby')) {
            $param[] = 'orderby='.$request->orderby;
        }

        if ($request->has('minExpireDate')) {
            $param[] = 'minExpireDate='.$request->minExpireDate;
        }

        if ($request->has('maxExpireDate')) {
            $param[] = 'maxExpireDate='.$request->maxExpireDate;
        }

        return $param;
    }
}
