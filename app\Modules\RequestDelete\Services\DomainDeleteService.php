<?php

namespace App\Modules\RequestDelete\Services;

use App\Events\DomainHistoryEvent;
use App\Mail\UserDeleteRequestMail;
use App\Modules\Client\Constants\DomainStatus;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Epp\Services\EppDomainService;
use App\Modules\RequestDelete\Services\DomainCancellationDispatcherService;
use App\Util\Constant\UserDomainStatus;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class DomainDeleteService
{
    private Carbon $now;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance()
    {
        return new self;
    }

    public function approveDeleteRequestSave($request)
    {
        $data = $request->all();

        // Check if domain name is already provided, otherwise look it up
        if (!isset($data['domainName']) || empty($data['domainName'])) {
            // Get domain name from domain ID
            app(AuthLogger::class)->info("Looking up domain with ID: " . $data['domainId']);
            $domain = DB::client()->table('domains')->where('id', $data['domainId'])->first();
            if (!$domain) {
                app(AuthLogger::class)->error("Domain not found with ID: " . $data['domainId'] . " in domains table");
                // Try to get domain info from registered_domains table
                $registeredDomain = DB::client()->table('registered_domains')
                    ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
                    ->where('registered_domains.domain_id', $data['domainId'])
                    ->select('domains.name', 'domains.id')
                    ->first();
                if ($registeredDomain) {
                    $data['domainName'] = $registeredDomain->name;
                    app(AuthLogger::class)->info("Found domain via registered_domains: " . $registeredDomain->name . " for ID: " . $data['domainId']);
                } else {
                    throw new \Exception("Domain not found with ID: " . $data['domainId']);
                }
            } else {
                $data['domainName'] = $domain->name;
                app(AuthLogger::class)->info("Found domain: " . $domain->name . " for ID: " . $data['domainId']);
            }
        } else {
            app(AuthLogger::class)->info("Domain name already provided: " . $data['domainName'] . " for ID: " . $data['domainId']);
        }

        // Get user information
        $user = $this->getUserByDomain($data['domainId']);
        if ($user) {
            $data['userID'] = $user->user_id;
            $data['userEmail'] = $user->email;
        }

        // Validate EPP status before dispatching job
        $this->validateEppStatus($data['domainName']);

        // Dispatch job for EPP deletion - all database updates will happen in the job after EPP validation
        app(AuthLogger::class)->info("Dispatching domain cancellation job for domain: " . $data['domainName'] . " (ID: " . $data['domainId'] . ")");
        DomainCancellationDispatcherService::instance()->dispatch([
            'domainId' => $data['domainId'],
            'domainName' => $data['domainName'],
            'userId' => $data['userID'] ?? null,
            'email' => $data['userEmail'] ?? null,
            'requestData' => $data,
            'isApproval' => true // Flag to indicate this is an approval process
        ]);
    }

    public function rejectDeleteRequestSave($request)
    {
        self::updateDomainDeletionRequestTable($request, 0);

        DB::client()->table('domains')->where('id', $request['domainId'])->update([
            'status' => DomainStatus::ACTIVE,
            'deleted_at' => null,
            'updated_at' => now(),
        ]);
    }

    public function createDeleteRequestSave($request)
    {
        $data = $request->all();

        // Check if domain name is already provided, otherwise look it up
        if (!isset($data['domainName']) || empty($data['domainName'])) {
            // Get domain name from domain ID
            app(AuthLogger::class)->info("Looking up domain with ID: " . $data['domainId']);
            $domain = DB::client()->table('domains')->where('id', $data['domainId'])->first();
            if (!$domain) {
                app(AuthLogger::class)->error("Domain not found with ID: " . $data['domainId'] . " in domains table");
                // Try to get domain info from registered_domains table
                $registeredDomain = DB::client()->table('registered_domains')
                    ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
                    ->where('registered_domains.domain_id', $data['domainId'])
                    ->select('domains.name', 'domains.id')
                    ->first();
                if ($registeredDomain) {
                    $data['domainName'] = $registeredDomain->name;
                    app(AuthLogger::class)->info("Found domain via registered_domains: " . $registeredDomain->name . " for ID: " . $data['domainId']);
                } else {
                    throw new \Exception("Domain not found with ID: " . $data['domainId']);
                }
            } else {
                $data['domainName'] = $domain->name;
                app(AuthLogger::class)->info("Found domain: " . $domain->name . " for ID: " . $data['domainId']);
            }
        } else {
            app(AuthLogger::class)->info("Domain name already provided: " . $data['domainName'] . " for ID: " . $data['domainId']);
        }

        // Get user information
        $user = $this->getUserByDomain($data['domainId']);
        if ($user) {
            $data['userID'] = $user->user_id;
            $data['userEmail'] = $user->email;
        }

        // Validate EPP status before dispatching job
        $this->validateEppStatus($data['domainName']);

        // Dispatch job for EPP deletion - all database updates will happen in the job after EPP validation
        app(AuthLogger::class)->info("Dispatching domain cancellation job for domain: " . $data['domainName'] . " (ID: " . $data['domainId'] . ")");
        DomainCancellationDispatcherService::instance()->dispatch([
            'domainId' => $data['domainId'],
            'domainName' => $data['domainName'],
            'userId' => $data['userID'] ?? null,
            'email' => $data['userEmail'] ?? null,
            'requestData' => $data,
            'isApproval' => false // Flag to indicate this is a create process
        ]);
    }

    public function localDelete($requestData, bool $skipUpdate = false)
    {
        if (!$skipUpdate) {
            self::updateDomainDeletionRequestTable($requestData);
        }

        $timestamp = now();

        $updates = [
            'status'     => UserDomainStatus::DELETED,
            'deleted_at' => $timestamp,
            'updated_at' => $timestamp,
        ];

        DB::client()->table('registered_domains')
            ->where('domain_id', $requestData['domainId'])
            ->update($updates);

        DB::client()->table('domains')
            ->where('id', $requestData['domainId'])
            ->update($updates);
    }

    private function updateDomainDeletionRequestTable($requestData, $authID = null)
    {
        $agentID = $authID ?? Auth::id();

        $exists = DB::client()
            ->table('domain_cancellation_requests')
            ->where('domain_id', $requestData['domainId'])
            ->exists();

        if (!$exists) {
            return;
        }

        $date = Carbon::parse($requestData['createdDate']);
        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? false : true;

        DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $requestData['domainId'])
            ->update([
                'support_agent_id'   => $agentID,
                'support_agent_name'=> Auth::user()->name . ' (' . Auth::user()->email . ')',
                'deleted_at'         => now(),
                'feedback_date'      => now(),
                'support_note'       => $requestData['support_note'] ?? 'Support note not provided',
                'is_refunded'        => $is_refunded,
            ]);
    }

    public function userNotification($requestData)
    {
        $userID = $requestData['userID'];
        $domainName = $requestData['domainName'];

        if (!$userID || !$domainName) return;

        DB::client()->table('notifications')->insert([
            'user_id'      => $userID,
            'title'        => 'Domain Deletion Request Approved',
            'message'      => 'Your request to delete the domain "' . $domainName . '" has been approved.',
            'redirect_url' => '/domain',
            'created_at'   => now(),
            'importance'   => 'important',
        ]);
    }

    public function userEmailNotification($requestData)
    {
        $domainName = $requestData['domainName'];
        $userEmail = $requestData['userEmail'];

        $message = [
            'subject'  => 'Domain Deletion Request Approved',
            'greeting' => 'Greetings!',
            'body'     => 'Your request to delete the domain "' . $domainName . '" has been approved. The domain will be removed from your account and queued for deletion shortly. This action is final and cannot be undone.',
            'text'     => Carbon::now()->format('Y-m-d H:i:s'),
            'sender'   => 'StrangeDomains Support',
        ];

        Mail::to($userEmail)->send(new UserDeleteRequestMail($message));

        self::emailTrack($userEmail, $message, $requestData['domainId']);
    }

    private function newDomainDelete($data)
    {
        $date = Carbon::parse($data['createdDate']);
        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? false : true;

        DB::client()->table('domain_cancellation_requests')->insert([
            'user_id'             => $data['userID'],
            'domain_id'           => $data['domainId'],
            'reason'              => $data['reason'],
            'support_agent_id'    => Auth::id(),
            'support_agent_name'  => Auth::user()->name . ' (' . Auth::user()->email . ')',
            'deleted_at'          => now(),
            'feedback_date'       => now(),
            'support_note'        => Auth::user()->name . ' (' . Auth::user()->email . ') deleted domain: ' . $data['domainName'],
            'is_refunded'         => $is_refunded,
        ]);
    }

    private function getUserByDomain($domainId) 
    {
        return DB::client()->table('registered_domains')
            ->select('users.id as user_id', 'users.email', 'users.first_name', 'users.last_name')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->where('domains.id', $domainId)
            ->first();
    }

    private function emailTrack($email, array $payload, $domainId = null) 
    {
        $userId = null;
        $userName = null;

        if ($domainId) {
            $user = $this->getUserByDomain($domainId);
            if ($user) {
                $userId = $user->user_id;
                $userName = $user->first_name . ' ' . $user->last_name;
            }
        }

        $emailSent = DB::client()->table('email_histories')
            ->insert([
                'user_id' => $userId,
                'name' => $userName ?? 'System',
                'recipient_email' => $email,
                'subject' => 'Domain Deletion Request Approved',
                'email_type' => 'Domain Deletion Request Approved',
                'email_body' => json_encode($payload),
                'attachment' => null,
                'created_at' => now(),
                'updated_at' => now()
            ]);

        return $emailSent;
    }

    public function domainHistory($data)
    {
        DB::client()->table('domain_transaction_histories')->insert([
            'domain_id' => $data['domainId'],
            'type'      => 'DOMAIN_DELETED',
            'user_id'   => $data['userID'],
            'status'    => 'success',
            'message'   => 'Domain "' . $data['domainName'] . '" deleted by ' . Auth::user()->name . ' (' . Auth::user()->email . ')',
            'payload'   => json_encode($data),
        ]);
    }

    private function validateEppStatus($domainName)
    {
        app(AuthLogger::class)->info("Validating EPP status for domain: {$domainName}");

        // Check EPP info for clientDeleteProhibited status
        $eppInfo = EppDomainService::instance()->callEppDomainInfo($domainName);
        if ($eppInfo['status'] === 'error') {
            $errorMessage = "Failed to get EPP info for domain {$domainName}: " . ($eppInfo['message'] ?? 'Unknown error');
            app(AuthLogger::class)->error($errorMessage);
            throw new \Exception($errorMessage);
        }

        // Check if domain has clientDeleteProhibited status
        if (isset($eppInfo['data']['status']) && in_array('clientDeleteProhibited', $eppInfo['data']['status'])) {
            $errorMessage = "Domain {$domainName} cannot be deleted because it has clientDeleteProhibited status";
            app(AuthLogger::class)->error($errorMessage);
            throw new \Exception($errorMessage);
        }

        app(AuthLogger::class)->info("EPP validation passed for domain: {$domainName}");
    }
}
