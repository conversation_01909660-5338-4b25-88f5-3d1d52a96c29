[2025-08-19 01:11:05] local.ERROR: {"query":[],"parameter":{"domainName":"handlerme.net","userEmail":"<EMAIL>","domainId":2,"createdDate":"2025-08-18 07:13:04","userID":7,"reason":"test"},"error":"Illuminate\\Validation\\ValidationException","message":"The reason field must be at least 10 characters.","code":0}  
[2025-08-19 01:11:19] local.ERROR: Attempt to read property "name" on null  
[2025-08-19 01:11:19] local.INFO: number of attempts: 1  
[2025-08-19 01:11:19] local.ERROR: Attempt to read property "name" on null  
[2025-08-19 01:11:19] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Attempt to read property \"name\" on null","code":0}  
[2025-08-19 01:18:31] local.INFO: Domain name already provided: handlerme.net for ID: 2  
[2025-08-19 01:18:31] local.INFO: Dispatching domain cancellation job for domain: handlerme.net (ID: 2)  
[2025-08-19 01:19:02] local.INFO: Processing EPP deletion for domain: handlerme.net with params: {"domainId":"2","domainName":"handlerme.net","userId":"7","email":"<EMAIL>","requestData":{"domainName":"handlerme.net","userEmail":"<EMAIL>","domainId":2,"createdDate":"2025-08-18 07:13:04","userID":7,"reason":"test tsetest setset set"}}  
[2025-08-19 01:19:04] local.ERROR: HTTP request returned status code 400:
{"message":"handlerme.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"handlerme.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-08-19 01:19:05] local.ERROR: HTTP request returned status code 404:
{"message":"Object does not exist","eppCode":2303,"status":"NOT_FOUND","statusCode":404}
 ; {"message":"Object does not exist","eppCode":2303,"status":"NOT_FOUND","statusCode":404}  
[2025-08-19 01:19:06] local.ERROR: Attempt to read property "name" on null  
[2025-08-19 01:19:06] local.INFO: number of attempts: 1  
[2025-08-19 01:19:06] local.ERROR: Attempt to read property "name" on null  
[2025-08-19 01:19:06] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Attempt to read property \"name\" on null","code":0}  
