<?php

namespace App\Modules\RequestDelete\Jobs;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\RequestDelete\Services\DomainCancellationJobService;
use App\Util\Constant\QueueConnection;
use App\Util\Helper\DomainParser;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Exception;
use Throwable;

class DomainCancellationJob implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $params;

    /**
     * if process takes longer than indicated timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    public function __construct(
        string $domainId,
        string $domainName,
        string $userId,
        string $email,
        array $requestData
    ) {
        $registry = DomainParser::getRegistryName($domainName);

        $this->params = [
            'domainId' => $domainId,
            'domainName' => $domainName,
            'userId' => $userId,
            'email' => $email,
            'requestData' => $requestData
        ];

        $this->onConnection(QueueConnection::DOMAIN_CANCELLATION);
        $this->onQueue("CANCELLATION-{$registry}");
    }

    public $uniqueFor = 3600;

    public function uniqueId(): string
    {
        return $this->params['domainId'];
    }

    public function handle(): void
    {
        try {
            DomainCancellationJobService::instance()->processEppDeletion($this->params);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());
            app(AuthLogger::class)->info('number of attempts: ' . $this->attempts());
            throw $e;
        }
    }

    public function backoff(): array
    {
        return [5, 10];
    }

    public function failed(?Throwable $exception): void
    {
        app(AuthLogger::class)->error($exception->getMessage());
    }
}
