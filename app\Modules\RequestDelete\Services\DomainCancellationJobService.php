<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Epp\Services\EppDomainService;
use App\Modules\RequestDelete\Services\DomainDeleteService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class DomainCancellationJobService
{
    private static ?self $instance = null;

    private function __construct()
    {
    }

    public static function instance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function processEppDeletion(array $params): void
    {
        $domainName = $params['domainName'];
        $requestData = $params['requestData'];

        app(AuthLogger::class)->info("Processing EPP deletion for domain: {$domainName} with params: " . json_encode($params));

        // First check EPP info for clientDeleteProhibited status
        $eppInfo = EppDomainService::instance()->callEppDomainInfo($domainName);
        if ($eppInfo['status'] === 'error') {
            app(AuthLogger::class)->error("Failed to get EPP info for domain {$domainName}: " . ($eppInfo['message'] ?? 'Unknown error'));
            return;
        }

        // Check if domain has clientDeleteProhibited status
        if (isset($eppInfo['data']['status']) && in_array('clientDeleteProhibited', $eppInfo['data']['status'])) {
            app(AuthLogger::class)->info("Domain {$domainName} cannot be deleted because it has clientDeleteProhibited status");
            return;
        }

        // Proceed with EPP deletion calls
        $eppDeleteResult = EppDomainService::instance()->callEppDomainDelete($domainName);
        if ($eppDeleteResult['status'] === 'error') {
            app(AuthLogger::class)->error("Failed to delete domain {$domainName} via EPP: " . ($eppDeleteResult['message'] ?? 'Unknown error'));
            return;
        }

        $datastoreDeleteResult = EppDomainService::instance()->callDatastoreDomainDelete($domainName);
        if ($datastoreDeleteResult['status'] === 'error') {
            app(AuthLogger::class)->error("Failed to delete domain {$domainName} via datastore: " . ($datastoreDeleteResult['message'] ?? 'Unknown error'));
            return;
        }

        // Continue with local deletion process
        $domainDeleteService = DomainDeleteService::instance();
        $domainDeleteService->localDelete($requestData);
        $domainDeleteService->userNotification($requestData);
        $domainDeleteService->userEmailNotification($requestData);
        $domainDeleteService->domainHistory($requestData);

        // Get registered_domain_id from domain_id
        $registeredDomain = DB::client()->table('registered_domains')
            ->where('domain_id', $requestData['domainId'])
            ->first();

        if ($registeredDomain) {
            // Insert into pending_domain_deletions table
            DB::client()->table('pending_domain_deletions')->insert([
                'registered_domain_id' => $registeredDomain->id,
                'deleted_by' => Auth::user()->email,
                'deleted_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } else {
            app(AuthLogger::class)->error("No registered domain found for domain_id: " . $requestData['domainId']);
        }

        app(AuthLogger::class)->info("Domain {$domainName} successfully processed for deletion");
    }
}
