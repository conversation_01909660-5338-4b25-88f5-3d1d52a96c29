<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Epp\Services\EppDomainService;
use App\Modules\RequestDelete\Services\DomainDeleteService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class DomainCancellationJobService
{
    private static ?self $instance = null;

    private function __construct()
    {
    }

    public static function instance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function processEppDeletion(array $params): void
    {
        $domainName = $params['domainName'];
        $requestData = $params['requestData'];
        $isApproval = $params['isApproval'] ?? false;

        app(AuthLogger::class)->info("Processing EPP deletion for domain: {$domainName} (isApproval: " . ($isApproval ? 'true' : 'false') . ")");

        // EPP validation was already done synchronously, proceed with EPP deletion calls
        $eppDeleteResult = EppDomainService::instance()->callEppDomainDelete($domainName);
        if ($eppDeleteResult['status'] === 'error') {
            app(AuthLogger::class)->error("Failed to delete domain {$domainName} via EPP: " . ($eppDeleteResult['message'] ?? 'Unknown error'));
            throw new \Exception("Failed to delete domain {$domainName} via EPP: " . ($eppDeleteResult['message'] ?? 'Unknown error'));
        }

        $datastoreDeleteResult = EppDomainService::instance()->callDatastoreDomainDelete($domainName);
        if ($datastoreDeleteResult['status'] === 'error') {
            app(AuthLogger::class)->error("Failed to delete domain {$domainName} via datastore: " . ($datastoreDeleteResult['message'] ?? 'Unknown error'));
            throw new \Exception("Failed to delete domain {$domainName} via datastore: " . ($datastoreDeleteResult['message'] ?? 'Unknown error'));
        }

        app(AuthLogger::class)->info("EPP deletion successful for domain {$domainName}, proceeding with database updates");

        // Handle database updates based on whether this is approval or create
        $domainDeleteService = DomainDeleteService::instance();

        if ($isApproval) {
            // For approval: just do local delete and notifications
            $domainDeleteService->localDelete($requestData);
        } else {
            // For create: create new domain delete record and do local delete
            $this->newDomainDelete($requestData);
            $domainDeleteService->localDelete($requestData, skipUpdate: true);
        }

        // Common operations for both scenarios
        $domainDeleteService->userNotification($requestData);
        $domainDeleteService->userEmailNotification($requestData);
        $domainDeleteService->domainHistory($requestData);

        // Get registered_domain_id from domain_id
        $registeredDomain = DB::client()->table('registered_domains')
            ->where('domain_id', $requestData['domainId'])
            ->first();

        if ($registeredDomain) {
            // Insert into pending_domain_deletions table
            DB::client()->table('pending_domain_deletions')->insert([
                'registered_domain_id' => $registeredDomain->id,
                'deleted_by' => Auth::user()->email,
                'deleted_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } else {
            app(AuthLogger::class)->error("No registered domain found for domain_id: " . $requestData['domainId']);
        }

        app(AuthLogger::class)->info("Domain {$domainName} successfully processed for deletion");
    }

    private function newDomainDelete($data)
    {
        $date = \Carbon\Carbon::parse($data['createdDate']);
        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? false : true;

        DB::client()->table('domain_cancellation_requests')->insert([
            'user_id'             => $data['userID'],
            'domain_id'           => $data['domainId'],
            'reason'              => $data['reason'],
            'support_agent_id'    => Auth::id(),
            'support_agent_name'  => Auth::user()->name . ' (' . Auth::user()->email . ')',
            'deleted_at'          => now(),
            'feedback_date'       => now(),
            'support_note'        => Auth::user()->name . ' (' . Auth::user()->email . ') deleted domain: ' . $data['domainName'],
            'is_refunded'         => $is_refunded,
        ]);
    }
}
