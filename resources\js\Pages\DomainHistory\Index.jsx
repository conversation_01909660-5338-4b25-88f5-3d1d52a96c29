import React, { useState } from "react";
import AdminLayout from "@/Layouts/AdminLayout";
import { MdFilterList, MdOutlineSettings } from "react-icons/md";
import SearchInput from "@/Components/DomainHistory/SearchInput";
import { usePage, Link, router } from "@inertiajs/react";
import Filter from "@/Components/DomainHistory/Filter";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import Item from "@/Components/DomainHistory/Item";
import { ImSortAlphaAsc, ImSortAlphaDesc } from "react-icons/im";
import { getEventValue } from "@/Util/TargetInputEvent";
import Checkbox from "@/Components/Checkbox";
import SecondaryButton from "@/Components/SecondaryButton";
import LoaderSpinner from "@/Components/LoaderSpinner";
import ConfirmationModal from "@/Components/Util/ConfirmationModal";
import SearchNoDomainFound from "@/Components/Domain/Search/SearchNoDomainFound";
import "react-toastify/dist/ReactToastify.css";
import { toast } from "react-toastify";

const SORT_TYPE = {
    NAME_ASC: "name:asc",
    NAME_DESC: "name:desc",
};

export default function DomainHistory() {
    const {
        domains = { data: [] },
        onFirstPage,
        onLastPage,
        nextPageUrl,
        previousPageUrl,
        itemCount = 0,
        total = 0,
        searchTerm: initialSearchTerm = "",
        transactionTypes,
        status,
    } = usePage().props;

    const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
    const [orderby, setOrderby] = useState(SORT_TYPE.NAME_ASC);
    const [selectedDomains, setSelectedDomains] = useState([]);
    const [selectAll, setSelectAll] = useState(false);
    const [loadingDomains, setLoadingDomains] = useState([]);
    const [hasSpinner, setSpinner] = useState(false);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [pendingAction, setPendingAction] = useState(null);

    const { pagination } = usePage().props;
    const filteredDomains = domains.data;
    const query = route().params;
    const [limit, setLimit] = useState(parseInt(query.limit) || 20);

    router.on("start", () => {
        setSpinner(true);
    });

    router.on("finish", () => {
        setSpinner(false);
    });

    const handleSelectAllChange = (e) => {
        const checked = getEventValue(e);
        setSelectAll(checked);
        const activeDomains = domains.data.filter(
            (item) => item.status === "ACTIVE"
        );
        const domain_ids = activeDomains.map((item) => item.id);
        setSelectedDomains(checked ? domain_ids : []);
    };

    const handleItemCheckboxChange = (domainId, isActive, checked) => {
        setSelectedDomains((prevSelectedDomains) => {
            return checked
                ? [...prevSelectedDomains, domainId]
                : prevSelectedDomains.filter((id) => id !== domainId);
        });
    };

    const handleSearchChange = (searchParams) => {
        const transformedParams = Object.fromEntries(
            Object.entries(searchParams).map(([key, value]) => [
                key,
                key === "status" || key === "email"
                    ? value.toLowerCase()
                    : value,
            ])
        );
        setSearchTerm(transformedParams);
        router.get(
            route("domain.history"),
            { ...transformedParams },
            { preserveState: true }
        );
    };

    const handlePageChange = (url) => {
        router.get(url, { ...searchTerm }, { preserveState: true });
    };

    const handleOrderToggle = () => {
        const newOrder =
            orderby === SORT_TYPE.NAME_ASC
                ? SORT_TYPE.NAME_DESC
                : SORT_TYPE.NAME_ASC;
        setOrderby(newOrder);
        router.get(
            route("domain.history"),
            { orderby: newOrder },
            { preserveState: true }
        );
    };

    const handleLimitChange = (e) => {
        const newLimit = parseInt(getEventValue(e));
        setLimit(newLimit);
        router.get(
            route("domain.history"),
            { ...route().params, limit: newLimit, page: 1 },
            {
                preserveScroll: true,
                preserveState: true,
            }
        );
    };

    const handleClientHoldAction = (action) => {
        const buttonStatus = getSelectedDomainsStatus();

        if (selectedDomains.length === 0) return;
        if (action === "update" && buttonStatus.disableHold) return;
        if (action === "remove" && buttonStatus.disableUnhold) return;

        setPendingAction(action);
        setIsModalOpen(true);
    };

    const handleConfirmAction = () => {
        if (!pendingAction) return;

        setLoadingDomains(selectedDomains);
        setIsModalOpen(false);

        router.post(
            route(`domain.client-hold.${pendingAction}`),
            {
                domain_ids: selectedDomains,
            },
            {
                preserveScroll: true,
                onSuccess: () => {
                    toast.success("Client domain is now in process.");
                    setSelectedDomains([]);
                    setSelectAll(false);
                    setLoadingDomains([]);
                    setPendingAction(null);
                },
                onError: (error) => {
                    setLoadingDomains([]);
                    setPendingAction(null);
                    toast.error(error.message);
                },
            }
        );
    };

    const handleCloseModal = () => {
        setIsModalOpen(false);
        setPendingAction(null);
    };

    const isClientHold = (domain) => {
        if (!domain.client_status) {
            return false;
        }

        const clientStatus = Array.isArray(domain.client_status)
            ? domain.client_status
            : typeof domain.client_status === "string"
            ? JSON.parse(domain.client_status)
            : [];

        return (
            Array.isArray(clientStatus) && clientStatus.includes("clientHold")
        );
    };

    const getSelectedDomainsStatus = () => {
        if (selectedDomains.length === 0)
            return {
                disableHold: true,
                disableUnhold: true,
            };

        const selectedDomainsData = domains.data.filter((domain) =>
            selectedDomains.includes(domain.id)
        );
        const domainsWithHold = selectedDomainsData.filter((domain) =>
            isClientHold(domain)
        );
        const domainsWithoutHold = selectedDomainsData.filter(
            (domain) => !isClientHold(domain)
        );

        if (domainsWithHold.length > 0 && domainsWithoutHold.length > 0) {
            return {
                disableHold: true,
                disableUnhold: true,
            };
        }

        return {
            disableHold: domainsWithHold.length > 0,
            disableUnhold: domainsWithHold.length === 0,
        };
    };

    const buttonStatus = getSelectedDomainsStatus();

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col space-y-4">
                <div>
                    <h1 className="text-4xl font-bold pl-3">Domains</h1>
                    <p className="text-gray-600 pl-3">
                        A summary of all changes to the domain name and its
                        related aliases over time.
                    </p>
                </div>
                <div className="flex items-center space-x-4 justify-end">
                    <SecondaryButton
                        onClick={() => handleClientHoldAction("update")}
                        disabled={
                            selectedDomains.length === 0 ||
                            buttonStatus.disableHold
                        }
                        className={`${
                            selectedDomains.length === 0 ||
                            buttonStatus.disableHold
                                ? "opacity-50 cursor-not-allowed"
                                : ""
                        }`}
                        title={
                            buttonStatus.disableHold &&
                            selectedDomains.length > 0
                                ? "You have selected domains with different statuses"
                                : ""
                        }
                    >
                        Client Hold
                    </SecondaryButton>
                    <SecondaryButton
                        onClick={() => handleClientHoldAction("remove")}
                        disabled={
                            selectedDomains.length === 0 ||
                            buttonStatus.disableUnhold
                        }
                        className={`${
                            selectedDomains.length === 0 ||
                            buttonStatus.disableUnhold
                                ? "opacity-50 cursor-not-allowed"
                                : ""
                        }`}
                        title={
                            buttonStatus.disableUnhold &&
                            selectedDomains.length > 0
                                ? "You have selected domains with different statuses"
                                : ""
                        }
                    >
                        Client Unhold
                    </SecondaryButton>
                </div>
                <div
                    className="flex justify-start"
                    style={{ position: "relative", top: "20px", left: "15px" }}
                >
                    <label className="mr-2 text-sm pt-1 text-gray-600">
                        Show
                    </label>
                    <select
                        value={limit}
                        onChange={handleLimitChange}
                        className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                    >
                        {[20, 25, 30, 40, 50, 100].map((val) => (
                            <option key={val} value={val}>
                                {val}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="flex justify-between items-center mt-4 pt-4 pb-4 pl-3">
                    <div className="flex items-center space-x-2">
                        <MdFilterList className="text-xl" />
                        <span>Filter:</span>
                        <Filter
                            onFilter={handleSearchChange}
                            transactionTypes={transactionTypes}
                        />
                    </div>

                    <SearchInput
                        onSearchChange={handleSearchChange}
                        placeholder="Search domain"
                    />
                </div>
                <div>
                    <table className="min-w-full bg-white">
                        <thead className="bg-gray-50">
                            <tr className="border-b">
                                <th className="py-2 px-4 text-left">
                                    <label className="flex items-center space-x-2">
                                        {domains.data.length === 0 ? (
                                            <Checkbox
                                                disabled={true}
                                                className="pointer-events-none opacity-0"
                                            />
                                        ) : (
                                            <Checkbox
                                                name="select_all"
                                                value="select_all"
                                                checked={
                                                    selectedDomains.length ===
                                                    domains.data.filter(
                                                        (item) =>
                                                            item.status ===
                                                            "ACTIVE"
                                                    ).length
                                                }
                                                handleChange={
                                                    handleSelectAllChange
                                                }
                                            />
                                        )}
                                        <button
                                            onClick={handleOrderToggle}
                                            disabled={domains.data.length === 0}
                                            className="flex items-center space-x-1"
                                        >
                                            <span>Domain</span>
                                            {orderby === SORT_TYPE.NAME_ASC ? (
                                                <ImSortAlphaAsc />
                                            ) : (
                                                <ImSortAlphaDesc />
                                            )}
                                        </button>
                                    </label>
                                </th>
                                <th className="py-2 px-4 text-left">
                                    Last Activity
                                </th>
                                <th className="py-2 px-4 text-left">
                                    Last Action By
                                </th>
                                <th className="py-2 px-4 text-left">
                                    Last Update
                                </th>
                                <th className="py-2 px-4 text-left">Status</th>
                                <th>
                                    <span className="text-xl">
                                        <MdOutlineSettings />
                                    </span>
                                </th>
                                <th className="py-2 px-4"></th>
                            </tr>
                        </thead>
                        <tbody className="text-sm">
                            {hasSpinner ? (
                                <tr>
                                    <td colSpan={6}>
                                        <div className="mx-auto container mt-8 flex flex-col px-28 rounded-lg">
                                            <LoaderSpinner
                                                ml="ml-96"
                                                h="h-12"
                                                w="w-12"
                                                position="absolute"
                                            />
                                            <br />
                                            <span className="relative top-9 left-72 ml-20">
                                                Loading Data...
                                            </span>
                                        </div>
                                    </td>
                                </tr>
                            ) : filteredDomains.length === 0 ? (
                                <tr>
                                    <td colSpan={6}>
                                        <SearchNoDomainFound />
                                    </td>
                                </tr>
                            ) : (
                                <>
                                    {Array.isArray(filteredDomains) &&
                                        filteredDomains.map((domain, index) => (
                                            <Item
                                                key={index}
                                                domain={domain}
                                                transactionTypes={
                                                    transactionTypes
                                                }
                                                isSelected={selectedDomains.includes(
                                                    domain.id
                                                )}
                                                onCheckboxChange={
                                                    handleItemCheckboxChange
                                                }
                                            />
                                        ))}
                                </>
                            )}
                        </tbody>
                    </table>
                </div>

                {hasSpinner ? (
                    " "
                ) : (
                    <div className="mt-6">
                        <CursorPaginate
                            onFirstPage={pagination.onFirstPage}
                            onLastPage={pagination.onLastPage}
                            nextPageUrl={pagination.nextPageUrl}
                            previousPageUrl={pagination.previousPageUrl}
                            itemCount={pagination.itemCount}
                            total={pagination.total}
                            itemName="domain"
                            onPageChange={handlePageChange}
                        />
                    </div>
                )}

                <ConfirmationModal
                    isOpen={isModalOpen}
                    onClose={handleCloseModal}
                    onConfirm={handleConfirmAction}
                    action={pendingAction}
                />
            </div>
        </AdminLayout>
    );
}
