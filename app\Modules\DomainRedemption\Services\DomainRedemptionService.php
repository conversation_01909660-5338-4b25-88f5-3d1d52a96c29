<?php

namespace App\Modules\DomainRedemption\Services;

use AdminHistoryType;
use App\Events\AdminActionEvent;
use App\Modules\DomainRedemption\Jobs\DomainRestoreApprovedJob;
use App\Modules\CustomLogger\Services\AuthLogger;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Modules\AdminHistory\Constants\HistoryType;
use Carbon\Carbon;

class DomainRedemptionService
{
    private const DEFAULT_AMOUNT = 50.00;

    private const DEFAULT_VALIDITY_DAYS = 30;

    public function __construct(
        private readonly DatabaseQueryService $queryService = new DatabaseQueryService()
    ) {}

    public static function instance(): self
    {
        return new self();
    }

    public function createPaymentWithDetails(array $ids, float $totalAmount, string $validUntil, ?string $note = null): void
    {
        $domains = $this->queryService->getRestorationDomainsWithUserData($ids);

        collect($domains)->each(fn($domain) =>
            $this->createRedemptionOrder($domain, $totalAmount, $validUntil, $note)
        );
    }

    public function delete(array $ids): void
    {
        // TODO: Implement delete functionality
    }

    private function createRedemptionOrder(array $domain, float $amount, string $validUntil, ?string $note = null): void
    {
        if ($this->redemptionOrderExists($domain['user_id'], $domain['domain_id'])) {
            app(AuthLogger::class)->info("Redemption order already exists for user {$domain['user_id']} and domain {$domain['name']}");
            return;
        }

        $uuid = (string) Str::uuid();

        DB::client()->table('redemption_orders')->insert([
            'user_id' => $domain['user_id'],
            'domain_id' => $domain['domain_id'],
            'total_amount' => $amount,
            'uuid' => $uuid,
            'paid_at' => null,
            'valid_until' => $validUntil,
            'note' => $note ?: null,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        app(AuthLogger::class)->info("Redemption order created for user {$domain['user_id']} and domain {$domain['name']}");
        event(new AdminActionEvent(auth()->user()->id, HistoryType::PAYMENT_CREATED, "Created redemption payment for domain: {$domain['name']} with total amount of $" . number_format($amount, 2) . " and valid until {$validUntil}" ));
    }

    private function redemptionOrderExists(string $userId, string $domainId): bool
    {
        return DB::client()->table('redemption_orders')
            ->where('user_id', $userId)
            ->where('domain_id', $domainId)
            ->whereNull('paid_at')
            ->exists();
    }

}