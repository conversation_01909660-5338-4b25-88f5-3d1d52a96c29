<?php

namespace App\Modules\DomainRedemption\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\DomainRedemption\Requests\DomainRedeemRequest;
use App\Modules\DomainRedemption\Requests\CreatePaymentRequest;
use App\Modules\DomainRedemption\Requests\ShowListRequest;
use Inertia\Inertia;

class DomainRedemptionController extends Controller
{
    public function index(ShowListRequest $request)
    {
        return Inertia::render('DomainRedemption/Index', $request->show());
    }

    public function restore(DomainRedeemRequest $request)
    {
        $request->restore();

        return Inertia::render('Notice/ConfirmationMessage', [
            'message' => 'Payment intent has been created successfully. Client can now proceed to checkout.',
            'postRouteName' => 'domain-redemption.view',
            'redirect' => [['route' => route('domain-redemption.view'), 'label' => 'Return to Domain Redemption']]
        ]);
    }

    public function createPayment(CreatePaymentRequest $request)
    {
        $request->createPayment();

        return Inertia::render('Notice/ConfirmationMessage', [
            'message' => 'Payment order has been created successfully. Client can now proceed to checkout.',
            'postRouteName' => 'domain-redemption.view',
            'redirect' => [['route' => route('domain-redemption.view'), 'label' => 'Return to Domain Redemption']]
        ]);
    }

    // public function approve(DomainRedeemRequest $request)
    // {
    //     $request->approve();

    //     return Inertia::render('Notice/ConfirmationMessage', [
    //         'message' => 'Domain restore accepted request has been processed.',
    //         'postRouteName' => 'domain-redemption.view',
    //         'redirect' => [['route' => route('domain-redemption.view'), 'label' => 'Return to Domain Redemption']]
    //     ]);
    // }

    public function delete(DomainRedeemRequest $request)
    {
        $request->delete();

        return Inertia::render('Notice/ConfirmationMessage', [
            'message' => 'Domain deletion request has been processed.',
            'postRouteName' => 'domain-redemption.view',
            'redirect' => [['route' => route('domain-redemption.view'), 'label' => 'Return to Domain Redemption']]
        ]);
    }
}