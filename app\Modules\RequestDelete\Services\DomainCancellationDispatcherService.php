<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\RequestDelete\Jobs\DomainCancellationJob;
use App\Util\Constant\QueueConnection;

class DomainCancellationDispatcherService
{
    private static ?self $instance = null;

    private function __construct()
    {
    }

    public static function instance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function dispatch(array $data): void
    {
        $job = new DomainCancellationJob(
            $data['domainId'],
            $data['domainName'],
            $data['userId'],
            $data['email'],
            $data['requestData']
        );

        $job->onConnection(QueueConnection::DOMAIN_CANCELLATION);
        
        dispatch($job);
    }
}
